2025-06-26 00:00:15:015 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/current-user/dashboard - 400
2025-06-26 00:03:09:39 [ERROR]: ❌ Request error: GET /api/v1/courses/creator/current-user - 400
2025-06-26 00:04:30:430 [ERROR]: ❌ Request error: GET /api/v1/messages/users/685c1b673a862730dd0a3b21/threads - 404
2025-06-26 00:04:30:430 [ERROR]: ❌ Request error: GET /api/v1/messaging/users/685c1b673a862730dd0a3b21/folders - 403
2025-06-26 00:04:30:430 [ERROR]: ❌ Request error: GET /api/v1/messaging/users/685c1b673a862730dd0a3b21/stats - 403
2025-06-26 00:06:54:654 [ERROR]: ❌ Request error: GET /api/v1/users/me - 401
2025-06-26 00:09:38:938 [ERROR]: ❌ Request error: GET /api/v1/messages/users/685c1b673a862730dd0a3b21/threads - 404
2025-06-26 00:09:38:938 [ERROR]: ❌ Request error: GET /api/v1/messaging/users/685c1b673a862730dd0a3b21/folders - 403
2025-06-26 00:09:39:939 [ERROR]: ❌ Request error: GET /api/v1/messaging/users/685c1b673a862730dd0a3b21/stats - 403
2025-06-26 00:09:44:944 [ERROR]: ❌ Request error: GET /api/v1/messaging/users/[object%20Object]/folders - 403
2025-06-26 00:09:44:944 [ERROR]: ❌ Request error: GET /api/v1/messaging/users/[object%20Object]/stats - 403
2025-06-26 00:10:06:106 [ERROR]: ❌ Request error: GET /api/v1/messages/users/[object%20Object]/threads - 404
2025-06-26 00:16:02:162 [ERROR]: ❌ Request error: GET /api/v1/messages/users/685c1b673a862730dd0a3b1e/threads - 404
2025-06-26 00:17:32:1732 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/current-user/dashboard - 400
2025-06-26 00:17:33:1733 [ERROR]: ❌ Request error: GET /api/v1/courses/creator/current-user - 400
2025-06-26 00:29:40:2940 [ERROR]: ❌ Request error: GET /api/v1/messages/users/685c1b673a862730dd0a3b1e/threads - 404
2025-06-26 00:29:41:2941 [ERROR]: ❌ Request error: GET /api/v1/courses/creator/current-user - 400
2025-06-26 00:29:47:2947 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/current-user/dashboard - 400
2025-06-26 00:38:49:3849 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/current-user/dashboard - 400
2025-06-26 00:38:51:3851 [ERROR]: ❌ Request error: GET /api/v1/courses/creator/current-user - 400
2025-06-26 00:44:31:4431 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/current-user/dashboard - 400
2025-06-26 00:44:33:4433 [ERROR]: ❌ Request error: GET /api/v1/courses/creator/current-user - 400
2025-06-26 12:39:05:395 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/685c1b673a862730dd0a3b21/activities - 401
2025-06-26 12:39:56:3956 [ERROR]: ❌ Request error: GET /api/v1/courses/creator/current-user - 400
2025-06-26 12:40:02:402 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/current-user/dashboard - 400
2025-06-26 12:57:33:5733 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/current-user/dashboard - 400
2025-06-26 12:57:34:5734 [ERROR]: ❌ Request error: GET /api/v1/courses/creator/current-user - 400
2025-06-26 13:11:37:1137 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/current-user/dashboard - 400
2025-06-26 13:15:54:1554 [ERROR]: ❌ Request error: GET /api/v1/courses/creator/current-user - 400
2025-06-26 13:16:58:1658 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/current-user/dashboard - 400
2025-06-26 13:22:39:2239 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/current-user/dashboard - 400
2025-06-26 13:22:41:2241 [ERROR]: ❌ Request error: GET /api/v1/courses/creator/current-user - 400
2025-06-26 14:12:26:1226 [ERROR]: ❌ Request error: GET /api/v1/analytics/teachers/current-user/dashboard - 400
2025-06-26 14:12:29:1229 [ERROR]: ❌ Request error: GET /api/v1/courses/creator/current-user - 400
